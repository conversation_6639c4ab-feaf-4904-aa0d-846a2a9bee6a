package com.mlc.workflow.core.editor.workstr.commands.gateway;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import com.mlc.workflow.core.editor.runtime.nodes.GatewayNode;
import com.mlc.workflow.core.editor.runtime.nodes.ConditionNode;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.workstr.autowire.WirePolicy;
import com.mlc.workflow.core.editor.workstr.commands.AbstractCommand;

import com.mlc.workflow.core.editor.workstr.validation.Validator;

import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;

/**
 * 添加网关命令
 * 在指定位置插入网关节点，并创建默认的条件分支
 */
public class AddGatewayCommand extends AbstractCommand {
    
    private final String insertAfterNodeId;
    private final int gatewayType; // 1-并行，2-唯一
    private final GatewayPlacementStrategy placementStrategy;
    
    public enum GatewayPlacementStrategy {
        /** 常规"不移动"策略 */
        NORMAL,
        /** "左侧移动"策略（把原后继节点移入左分支） */
        LEFT_MOVE
    }
    
    public AddGatewayCommand(WirePolicy wirePolicy, Validator validator, 
                           String insertAfterNodeId, int gatewayType, 
                           GatewayPlacementStrategy placementStrategy) {
        super(wirePolicy, validator);
        this.insertAfterNodeId = insertAfterNodeId;
        this.gatewayType = gatewayType;
        this.placementStrategy = placementStrategy != null ? placementStrategy : GatewayPlacementStrategy.NORMAL;
    }
    
    @Override
    protected CommandResult validateSpecific(ProcessNode processNode) {
        CommandResult baseResult = super.validateSpecific(processNode);
        if (!baseResult.isSuccess()) {
            return baseResult;
        }
        
        if (insertAfterNodeId == null || insertAfterNodeId.trim().isEmpty()) {
            return CommandResult.failure("插入位置节点ID不能为空");
        }
        
        Map<String, BaseNode> flowNodeMap = processNode.getFlowNodeMap();
        BaseNode insertAfterNode = flowNodeMap.get(insertAfterNodeId);
        
        if (insertAfterNode == null) {
            return CommandResult.failure("插入位置节点不存在: " + insertAfterNodeId);
        }
        
        if (!(insertAfterNode instanceof IRoutable)) {
            return CommandResult.failure("插入位置节点不支持路由: " + insertAfterNodeId);
        }
        
        if (gatewayType != 1 && gatewayType != 2) {
            return CommandResult.failure("无效的网关类型: " + gatewayType + "，必须是1(并行)或2(唯一)");
        }
        
        return CommandResult.success();
    }
    
    @Override
    protected CommandResult executeOperation(ProcessNode processNode) {
        Map<String, BaseNode> flowNodeMap = processNode.getFlowNodeMap();
        BaseNode insertAfterNode = flowNodeMap.get(insertAfterNodeId);
        IRoutable routableInsertAfter = (IRoutable) insertAfterNode;
        
        // 创建网关节点
        GatewayNode gateway = createGateway();
        
        // 创建两个默认的条件分支节点
        ConditionNode condition1 = createConditionNode(gateway.getId());
        ConditionNode condition2 = createConditionNode(gateway.getId());
        
        // 设置网关的分支列表
        gateway.getFlowIds().add(condition1.getId());
        gateway.getFlowIds().add(condition2.getId());
        
        // 根据放置策略执行不同的连接逻辑
        if (placementStrategy == GatewayPlacementStrategy.LEFT_MOVE) {
            executeLeftMoveStrategy(processNode, gateway, condition1, condition2, routableInsertAfter);
        } else {
            executeNormalStrategy(processNode, gateway, condition1, condition2, routableInsertAfter);
        }
        
        // 添加节点到流程图
        flowNodeMap.put(gateway.getId(), gateway);
        flowNodeMap.put(condition1.getId(), condition1);
        flowNodeMap.put(condition2.getId(), condition2);
        
        return CommandResult.success("成功添加网关节点");
    }
    
    /**
     * 执行常规"不移动"策略
     */
    private void executeNormalStrategy(ProcessNode processNode, GatewayNode gateway, 
                                     ConditionNode condition1, ConditionNode condition2, 
                                     IRoutable insertAfterNode) {
        String originalNextId = insertAfterNode.getNextId();
        
        // 重接：A.nextId=G、G.prveId=A、G.nextId=B、B.prveId=G
        insertAfterNode.setNextId(gateway.getId());
        gateway.setPrveId(((BaseNode) insertAfterNode).getId());
        gateway.setNextId(originalNextId);
        
        // 如果原来有后继节点，更新其prveId指向网关
        if (originalNextId != null && !originalNextId.trim().isEmpty() && !"99".equals(originalNextId)) {
            Map<String, BaseNode> flowNodeMap = processNode.getFlowNodeMap();
            BaseNode originalNext = flowNodeMap.get(originalNextId);
            if (originalNext instanceof IRoutable) {
                ((IRoutable) originalNext).setPrveId(gateway.getId());
            }
        }
        
        // 两个条件分支都指向空（到END）
        condition1.setNextId("");
        condition2.setNextId("");
    }
    
    /**
     * 执行"左侧移动"策略
     */
    private void executeLeftMoveStrategy(ProcessNode processNode, GatewayNode gateway, 
                                       ConditionNode condition1, ConditionNode condition2, 
                                       IRoutable insertAfterNode) {
        String originalNextId = insertAfterNode.getNextId();
        
        // 重接：A.nextId=G、G.prveId=A
        insertAfterNode.setNextId(gateway.getId());
        gateway.setPrveId(((BaseNode) insertAfterNode).getId());
        
        // 迁移：C1.nextId=B，并把原B的prveId改为C1
        condition1.setNextId(originalNextId);
        if (originalNextId != null && !originalNextId.trim().isEmpty() && !"99".equals(originalNextId)) {
            Map<String, BaseNode> flowNodeMap = processNode.getFlowNodeMap();
            BaseNode originalNext = flowNodeMap.get(originalNextId);
            if (originalNext instanceof IRoutable) {
                ((IRoutable) originalNext).setPrveId(condition1.getId());
            }
        }
        
        // G.nextId=""（两条默认分支均到END，汇合后不再有后继）
        gateway.setNextId("");
        
        // 保持C2.nextId=""
        condition2.setNextId("");
    }
    
    /**
     * 创建网关节点
     */
    private GatewayNode createGateway() {
        GatewayNode gateway = new GatewayNode();
        gateway.setId(generateId());
        gateway.setName("分支");
        gateway.setGatewayType(gatewayType);
        gateway.setFlowIds(new ArrayList<>());
        return gateway;
    }
    
    /**
     * 创建条件分支节点
     */
    private ConditionNode createConditionNode(String gatewayId) {
        ConditionNode condition = new ConditionNode();
        condition.setId(generateId());
        condition.setName("");
        condition.setPrveId(gatewayId);
        condition.setNextId("");
        return condition;
    }
    
    /**
     * 生成唯一ID
     */
    private String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    @Override
    public String getDescription() {
        return String.format("添加%s网关到节点%s后面，使用%s策略", 
                           gatewayType == 1 ? "并行" : "唯一", 
                           insertAfterNodeId, 
                           placementStrategy == GatewayPlacementStrategy.LEFT_MOVE ? "左侧移动" : "常规");
    }
}
