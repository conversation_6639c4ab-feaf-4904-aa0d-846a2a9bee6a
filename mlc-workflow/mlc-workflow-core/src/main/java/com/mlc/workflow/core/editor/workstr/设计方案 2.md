# 1. 领域建模与核心抽象

**1.1 节点能力接口对齐**

* **路由能力**：所有可连线节点实现 `IRoutable`，统一读写 `nextId/prveId` 与合法性检查（包括允许特殊结束值 `"99"`）。
* **分支能力**：网关实现 `IHasBranches`，维护 `flowIds` 顺序列表（分支顺序即 UI 顺序），并提供有效性检查与新增方法。
* **条件能力**：条件分支节点实现 `IHasConditions`，统一维护 `operateCondition`。
* **子流程能力**：需要嵌套子流程的节点实现 `IHasSubProcess`，并可用内置校验判定子流程是否完整。

**1.2 关键结构约定（从样例抽取的稳定事实）**

* **网关节点**（`typeId:1`，名称“分支”）承载：

    * `flowIds`: 指向多个“条件分支节点”（`typeId:2`）的**首节点ID**有序列表；
    * `gatewayType`: 1 并行、2 唯一（互斥）。
* **条件分支节点**（`typeId:2`）：

    * `prveId` = 网关ID；`nextId` = 本分支首个“业务节点”；`operateCondition` 为条件组。
* **分支后继（汇合）语义**：

    * **分支“尾节点”通常以 `nextId:""` 或 `99` 结束**；并**不直接指向网关的后继**。例如左分支尾部“抄送”`nextId:""`；右分支尾部“发送站内通知”`nextId:""`。
    * **网关自身的 `nextId` 才是“汇合后的后继”**；汇合后第一个节点的 `prveId` 指向网关本身（而不是各分支的尾节点）。样例中“通知下级部门”`prveId=网关ID`，`nextId:"99"`。

> 上述“**分支尾到 END，网关 nextId 指向汇合后继**”是本方案设计一切连线算法的**核心不变式**。

---

# 2. 遍历：BFS / DFS

**设计模式：Strategy 策略模式 + Iterator**

* **图视图**：从 `flowNodeMap` 构建只读邻接访问器：

    * **常规边**：`u → u.nextId`（忽略空/`99` 的终止）。
    * **网关出边**：`G → G.flowIds[i]`（到各“条件分支节点”）；以及**一条“汇合边”**`G → G.nextId`。
* **DFS/BFS 策略**：提供 `DepthFirstTraversal` 与 `BreadthFirstTraversal` 两种策略，实现相同的 `GraphIterator` 接口。
* **环与重复**：维护 `visited` 与 `onStack`（DFS）集合；对**子流程**节点若需“内联遍历”，以 `IHasSubProcess.hasValidSubProcess()` 判定；也可作为黑盒仅视为单节点。
* **终止条件**：`nextId == ""` 或 `"99"` 视为终止。

---

# 3. 结构操作（Command 命令模式）

> 每个结构变更都是一个**命令对象**，在一次事务中执行**校验 → 自动连线 → 持久化**三个阶段。可组合到“操作批”（Composite）。

## 3.1 网关操作

### 3.1.1 新增网关（默认并行），两种落位策略

设插入位置为 `A → B`（`A.nextId = B`）：

* **常规“不移动”**

    1. 新建网关 `G(gatewayType=1)`；新建两条条件分支 `C1, C2`（`C*.prveId=G`，`C*.nextId=""`）；将 `G.flowIds=[C1,C2]`。
    2. **重接**：`A.nextId=G`、`G.prveId=A`、`G.nextId=B`、`B.prveId=G`（保持“网关后继 = 汇合后继”的不变式）。
    3. 结果：分支各自走到 END，**待汇合（并行全部完成/唯一命中后）再进入 `B`**。

* **“左侧移动”**（把原 `B` 及其以下链路整体移入左分支）

    1. 同上创建 `G, C1, C2`；
    2. **重接**：`A.nextId=G`、`G.prveId=A`；
    3. **迁移**：`C1.nextId=B`，并把**原 `B` 的 `prveId` 改为 `C1`**（此后整条链仍保持其内部 `nextId/prveId`）；
    4. `G.nextId=""`（两条默认分支均到 END，**汇合后不再有后继**）；
    5. 保持 `C2.nextId=""`。

> 该策略完全符合“左侧：把网关下面所有节点都放在左侧分支；两条默认分支都直接连到结束”的业务语义。

**副作用与校验**

* 若 `gatewayType` 为**唯一分支**，需保证**至少一条默认兜底分支**（最后一条条件为 True，或显式 default 标记）。
* 并行切换到唯一：需要**检查并提示**条件**互斥与覆盖**；唯一切换到并行：允许保留条件，仅作为执行过滤。

### 3.1.2 删除网关

* 若**分支数 > 1**：

    * 禁止直接删除，或要求先合并/删除分支至 1 条（见 3.4 特例）。
* 若**只剩 1 条分支**：**触发“网关扁平化”**（见 3.4）。

### 3.1.3 修改网关类型（并行⇄唯一）

* **状态模式（State）/策略模式（Strategy）**：

    * `GatewayStrategy` 抽象：`validateOnEnter()`、`onSplit()`、`onJoin()`、`onBranchAdd()`；
    * `ParallelGateway` 与 `ExclusiveGateway` 两实现。
* **切换流程**：

    1. `validateOnEnter`（互斥覆盖/默认分支校验）；
    2. 更新 `gatewayType`；
    3. 重算执行语义（结构指针不改，维持“分支尾到 END、网关 nextId=汇合后继”不变式）。

## 3.2 分支操作（增/删/排序/复制）

* **新增分支**：创建新的条件分支节点 `Ci`（`prveId=G`、`nextId=""`），插入 `G.flowIds` 的目标位置（顺序即优先级/显示顺序）。
* **删除分支**：删除 `Ci` 及**自 `Ci.nextId` 起的整条链**（直至 `""/99`）。更新 `G.flowIds`。
* **调整分支顺序**：仅重排 `G.flowIds`，不改指针（因为分支头均以 `prveId=G`，尾至 END）。
* **复制分支**：深度克隆 `Ci` 与其后链路，**生成新 ID 映射**，保持“首节点 `prveId=G`、尾至 END”的不变式后加入 `G.flowIds`。

**副作用**

* 唯一分支时的**条件覆盖/互斥**需重新校验；若违反则**阻断提交**并给出修正建议（如自动生成兜底分支）。

## 3.3 普通节点操作（增/删/改）

* **新增**：

    * **插入到 `A → B`**：`A.nextId=X`、`X.prveId=A`、`X.nextId=B`、`B.prveId=X`。
    * 若 `B` 是网关：保持 `B.prveId=上游` 的不变式，即插入点在**网关之前**或**某条分支链上**分别处理。
* **删除**：

    * **删除 `X`**（上下文 `A ← X → B`）：`A.nextId=B`、`B.prveId=A`；若 `A` 为网关，按“不变式”改成 `B.prveId=G`。
* **修改**：只改属性；涉及路由重定向时走“上下文自动连线”策略（见 §4）。

## 3.4 特殊规则：**删除网关分支后只剩一条分支 ⇒ 彻底移除网关**

> 业务规则要求：**剩余分支“当作普通链路”接回上下文**。

设剩余分支为 `C1`，其首个业务节点为 `H`，分支尾为 `T`，网关 `G` 的上下文为 `P → G → S`（`S` 为汇合后继）：

* **扁平化步骤**：

    1. **删除** `G` 与 `C1`；
    2. **重接**：`P.nextId = H`、`H.prveId = P`；
    3. `T.nextId = S`；
    4. 若 `S` 存在（非 `""/99`），则 `S.prveId = T`；否则链路以 `T` 结束。

> 这样即可把分支链提升为“主链”，**完全移除网关语义**与条件层。

---

# 4. 通用“上下文自动连线”策略（贯穿所有 CRUD）

**设计模式：Policy/Strategy + Template Method**

定义一套**与结构语义解耦**的连接原语：

* **Connect(a → b)**：`a.nextId=b`、`b.prveId=a`。
* **Disconnect(a ⇢ ?)**：`a.nextId=""`（或 `"99"` 由调用者决定）。
* **InsertBetween(a, x, b)**：等价于 `Connect(a→x)` + `Connect(x→b)`。
* **Replace(x → r)**：保持 `x.prveId` 与 `x.nextId` 的两端，用 `r` 替换中点。
* **GatewayAwareConnect**（**关键**）：

    * **若 b 是“网关后继”**（即 `b.prveId` 应该指向某网关 `G`），则**强制 `b.prveId=G`**，而不是最近的分支尾或插入节点，保持样例中的**汇合不变式**。例如“通知下级部门”的 `prveId` 指向网关。

---

# 5. `nextId/prveId` 的**稳定算法**（尤其在网关内）

> 目标：无论 CRUD 如何组合，都**不打破**样例确证的指针模式。

**5.1 网关内的局部不变式**

* `G.flowIds` 中每个 `Ci.prveId=G`；`Ci.nextId` 指向本分支链头。
* **分支尾**以 `""` 或 `"99"` 结束，不指向 `G.nextId`。
* **汇合后继**：`G.nextId=S`，且 `S.prveId=G`。

**5.2 操作后的指针修复规则（模板）**

* **插入前置**（在 `P → X` 前插入 `A`）：`Connect(P→A)`、`Connect(A→X)`。
* **插入到网关之前**（`P → G`）：`Connect(P→A)`、`Connect(A→G)`；**不改 `G.nextId`/`S.prveId`**。
* **插入到分支链内**：常规 `InsertBetween`。
* **删除普通节点**：`Connect(P→S)`。
* **删除分支**：移除 `Ci` 与其链路；若 `flowIds.size()==1` 触发§3.4扁平化。
* **修改网关类型**：**不改任何指针**，仅改 `gatewayType` 和条件一致性。
* **复制分支**：新链保持“首连 G、尾到 END”。

**5.3 子流程指针**

* 子流程节点 `nextId/prveId` 按普通节点规则处理；子流程自身在 `processNode` 内部另有独立 `startEventId/flowNodeMap/nextId`，与父流程通过进入/返回协议衔接（样例中 `typeId:26` 节点内嵌子流程）。

---

# 6. 数据库同步与“撤销/重做”语义

**6.1 持久化策略（Unit of Work + Repository + 事务）**

* **聚合根**：`ProcessNode`（含 `startEventId`、`flowNodeMap`）。
* **写入模型**：

    * **文档型**：直接存储更新后的 `flowNodeMap` JSON（最简、与样例一致）。
    * **或** 规范化表（Nodes/Branches/Conditions），供查询与审计。
* **事务**：每个命令执行**单事务提交**，写前进行**结构校验**与**不变式检查**。

**6.2 版本与撤销/重做**

> 你的场景：撤销/重做 = **删除数据库中正在操作的版本**，不是内存回滚。

* **版本头指针（HEAD）** + **工作副本**：

    1. 进入编辑 ⇒ 从 HEAD 拷贝生成“工作副本”；
    2. 每次命令直接写回“工作副本”；
    3. **撤销**：删除当前“工作副本”，回到上一稳定版本；
    4. **重做**：按你的约定即再次执行命令（没有内存栈依赖）。
* **并发控制**：版本行加 `etag/updated_at` 实现**乐观锁**。
* **事件外发（Outbox/Domain Events）**：结构变更成功后投递“FlowUpdated”。

---

# 7. 校验与一致性检查（提交前）

* **引用完整性**：所有 `nextId/prveId/flowIds` 的 ID 要么存在，要么为 `""/99`。
* **单入度约束**：除起点外，每节点 `prveId` 恰有一值（网关后继的 `prveId=G`）。样例中“通知下级部门”的 `prveId` 指向网关。
* **网关合法性**：

    * `flowIds` 非空且指向的都是 `typeId:2`；每个 `Ci.prveId=G`；
    * 并行：无条件覆盖要求；唯一：条件**互斥且覆盖**（默认分支或兜底条件）。
* **尾节点终止**：分支尾 `nextId=""` 或 `99`，示例“抄送”“发送站内通知”即如此。

---

# 8. 典型流程示例（映射到样例 JSON 的指针形态）

* **起点** `startEventId → 审批流程（typeId:26） → 网关（typeId:1）`：`26.nextId=网关`。
* **网关（唯一型）**：`flowIds=[C_left,C_right]`、`gatewayType=2`。
* **左分支**：`C_left.prveId=网关`、`C_left.nextId=发送站内通知 → 抄送 → ""`。
* **右分支**：`C_right.prveId=网关`、`C_right.nextId=发送站内通知 → ""`。
* **汇合后继**：`网关.nextId=通知下级部门` 且 `通知下级部门.prveId=网关`，其后 `nextId:"99"`。

---

# 9. 组件划分与设计模式落地

* **GraphIndex（只读索引）**：基于 `flowNodeMap` 构建邻接访问（网关出边 + 汇合边 + 常规边）。

    * **模式**：Facade（为遍历/校验提供统一入口）。
* **Traversal**：`TraversalStrategy`（BFS/DFS 两实现）。
* **AutoWire**：`WirePolicy`（普通/GatewayAware 两实现）。
* **GatewayStrategy**：`ParallelGateway` / `ExclusiveGateway`。
* **Commands**：`AddGatewayCommand`、`RemoveGatewayCommand`、`ChangeGatewayTypeCommand`、`AddBranchCommand`、`RemoveBranchCommand`、`ReorderBranchCommand`、`CloneBranchCommand`、`AddNodeCommand`、`DeleteNodeCommand`、`UpdateNodeCommand`。

    * **模式**：Command + Composite（批量），Template Method（执行模板：校验→连线→持久化）。
* **Repository + UnitOfWork**：持久化 `ProcessNode` 文档/表。
* **Validator**：结构与业务校验（可链式职责）。

---

# 10. 边界与异常处理（要点）

* **ID 生成**：所有新增节点/分支使用全局唯一 ID，落库前一次性替换到 `flowNodeMap`。
* **删除**：先断开出入边，再物理删除；**注意**在网关场景下**严禁**让后继节点 `prveId` 指向分支尾，违背不变式。
* **子流程复制**：可选“引用同一子流程”或“克隆一个新子流程”；若引用同一子流程，复制分支时只复制外层指针。
* **校验失败**：阻断提交，回滚事务，返回可自动修复建议（如为唯一分支自动添加 default 兜底）。
