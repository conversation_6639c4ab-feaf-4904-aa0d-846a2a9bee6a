package com.mlc.workflow.core.editor.runtime.beans;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import io.nop.api.core.annotations.data.DataBean;
import jakarta.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.UUID;
import lombok.Data;

import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程节点模型
 */
@Getter
@Setter
@DataBean
public class ProcessNode {
    
    /**
     * 流程ID
     */
    @NotBlank
    private String id;

    /**
     * 开始事件ID
     */
    @NotBlank
    private String startEventId;
    
    /**
     * 流程节点映射表
     */
    @NotBlank
    @JsonProperty("flowNodeMap")
    private Map<String, BaseNode> flowNodeMap;
    
    /**
     * 是否为子流程
     */
    private Boolean child;
    
    /**
     * 执行ID列表
     */
    private List<String> execIds = new ArrayList<>();
    
    /**
     * 待执行ID列表
     */
    private List<String> execPendingIds = new ArrayList<>();

    public String toJson() {

        // 将对象转换为JSON字符串, 并去掉为null的字段
        ObjectMapper objectMapper = new ObjectMapper().setSerializationInclusion(Include.NON_NULL);
        try {
            return objectMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
